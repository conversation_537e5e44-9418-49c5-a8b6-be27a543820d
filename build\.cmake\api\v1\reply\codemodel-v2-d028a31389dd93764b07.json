{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.8"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "gerrit_client", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-7594cef3fe578a6aebfe.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-1567d347d11f6adb23c9.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "gerrit_client::@6890427a1f51a3e7e1df", "jsonFile": "target-gerrit_client-Debug-394ef8a3f91bf536d646.json", "name": "gerrit_client", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.8"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "gerrit_client", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-7594cef3fe578a6aebfe.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-1567d347d11f6adb23c9.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "gerrit_client::@6890427a1f51a3e7e1df", "jsonFile": "target-gerrit_client-Release-5fd6bb03e9f33973699e.json", "name": "gerrit_client", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.8"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "gerrit_client", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-7594cef3fe578a6aebfe.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-1567d347d11f6adb23c9.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "gerrit_client::@6890427a1f51a3e7e1df", "jsonFile": "target-gerrit_client-MinSizeRel-faebe211cb643286b5b5.json", "name": "gerrit_client", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.8"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "gerrit_client", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-7594cef3fe578a6aebfe.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-1567d347d11f6adb23c9.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "gerrit_client::@6890427a1f51a3e7e1df", "jsonFile": "target-gerrit_client-RelWithDebInfo-721d8a4d806d010cd7d1.json", "name": "gerrit_client", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/git/gerrit_client/build", "source": "D:/git/gerrit_client"}, "version": {"major": 2, "minor": 6}}