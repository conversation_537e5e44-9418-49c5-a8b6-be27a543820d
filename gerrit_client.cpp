// gerrit_client.cpp: 定义应用程序的入口点。
//

#include "gerrit_client.h"
#include "easy_direct_composition/easy_direct_composition.h"

using namespace std;

// Provides the entry point to the application.
INT WINAPI wWinMain(_In_ HINSTANCE hInstance, _In_opt_ HINSTANCE, _In_ LPWSTR, _In_ INT)
{
	// Initialize the Easy Direct Composition library
	edc::initialize();

	// Create the Direct Composition environment
	edc::Environment dc_inst(hInstance, edc::Object::get_root());
	assert(SUCCEEDED(dc_inst.Initialize()));

	// Create a visual and surface
	edc::Object visual1 = dc_inst.makeVisual(dc_inst.getRootVisual());
	edc::Object surface1 = dc_inst.createSurfaceForVisual(visual1, 100, 100);

	// Create surface helper and add a rectangle
	edc::SurfaceHelper surface_helper(surface1);
	edc::RectData rect_data = edc::make_rect(0, 0, 100, 100, 0.6f, 1.0f, 1.0f, 0.5f);
	surface_helper.addRect("rect1", rect_data);
	surface_helper.compile();

	// Commit changes and run the message loop
	dc_inst.commit();
	return dc_inst.Run();
}

