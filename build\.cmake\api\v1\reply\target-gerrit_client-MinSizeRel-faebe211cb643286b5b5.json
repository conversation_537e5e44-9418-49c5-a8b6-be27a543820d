{"artifacts": [{"path": "MinSizeRel/gerrit_client.exe"}, {"path": "MinSizeRel/gerrit_client.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "add_compile_options", "add_compile_definitions", "set_property"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 23, "parent": 0}, {"command": 1, "file": 0, "line": 34, "parent": 0}, {"command": 2, "file": 0, "line": 13, "parent": 0}, {"command": 3, "file": 0, "line": 15, "parent": 0}, {"command": 4, "file": 0, "line": 38, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O1 /Ob1 /DNDEBUG -std:c++20"}, {"backtrace": 3, "fragment": "/utf-8"}], "defines": [{"backtrace": 4, "define": "UNICODE"}, {"backtrace": 4, "define": "_UNICODE"}], "language": "CXX", "languageStandard": {"backtraces": [5], "standard": "20"}, "sourceIndexes": [0, 5, 7, 9]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /MD /O1 /Ob1 /DNDEBUG"}, {"backtrace": 3, "fragment": "/utf-8"}], "defines": [{"backtrace": 4, "define": "UNICODE"}, {"backtrace": 4, "define": "_UNICODE"}], "language": "C", "sourceIndexes": [3]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "gerrit_client::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O1 /Ob1 /DNDEBUG", "role": "flags"}, {"fragment": "/machine:X86 /INCREMENTAL:NO /subsystem:windows", "role": "flags"}, {"backtrace": 2, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "d2d1.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "dcomp.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "ole32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "gerrit_client", "nameOnDisk": "gerrit_client.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 3, 5, 7, 9]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 2, 4, 6, 8]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "gerrit_client.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "gerrit_client.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "easy_direct_composition/src/obj_tree.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "easy_direct_composition/src/obj_tree.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "easy_direct_composition/src/obj_helper.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "easy_direct_composition/src/obj_helper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "easy_direct_composition/src/dc_env.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "easy_direct_composition/src/dc_env.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "easy_direct_composition/src/dc_surface.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "easy_direct_composition/src/dc_surface.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}