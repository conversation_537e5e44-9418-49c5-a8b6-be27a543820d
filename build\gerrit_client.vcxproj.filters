﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\git\gerrit_client\gerrit_client.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\git\gerrit_client\easy_direct_composition\src\obj_tree.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\git\gerrit_client\easy_direct_composition\src\obj_helper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\git\gerrit_client\easy_direct_composition\src\dc_env.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\git\gerrit_client\easy_direct_composition\src\dc_surface.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\git\gerrit_client\gerrit_client.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\git\gerrit_client\easy_direct_composition\src\obj_tree.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\git\gerrit_client\easy_direct_composition\src\obj_helper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\git\gerrit_client\easy_direct_composition\src\dc_env.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\git\gerrit_client\easy_direct_composition\src\dc_surface.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\git\gerrit_client\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{9565A1DF-B51D-38E2-AA2E-D335AC80CB2F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{954EF8EC-6764-3967-A9BC-0100B4B526E9}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
