{"cmake": {"generator": {"multiConfig": true, "name": "Visual Studio 17 2022", "platform": "win32"}, "paths": {"cmake": "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "cpack": "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cpack.exe", "ctest": "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/ctest.exe", "root": "D:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.28"}, "version": {"isDirty": false, "major": 3, "minor": 28, "patch": 3, "string": "3.28.3-msvc11", "suffix": "msvc11"}}, "objects": [{"jsonFile": "codemodel-v2-d028a31389dd93764b07.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "cache-v2-dc726350b234d1afb536.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-7ecd94d344deaafe19bd.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-0f917c55b4a1bcf28887.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-dc726350b234d1afb536.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-d028a31389dd93764b07.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "toolchains-v1-0f917c55b4a1bcf28887.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-7ecd94d344deaafe19bd.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}