# CMakeList.txt: gerrit_client 的 CMake 项目，在此处包括源代码并定义
# 项目特定的逻辑。
#
cmake_minimum_required (VERSION 3.8)
set(CMAKE_WIN32_EXECUTABLE ON)

# 如果支持，请为 MSVC 编译器启用热重载。
if (POLICY CMP0141)
  cmake_policy(SET CMP0141 NEW)
  set(CMAKE_MSVC_DEBUG_INFORMATION_FORMAT "$<IF:$<AND:$<C_COMPILER_ID:MSVC>,$<CXX_COMPILER_ID:MSVC>>,$<$<CONFIG:Debug,RelWithDebInfo>:EditAndContinue>,$<$<CONFIG:Debug,RelWithDebInfo>:ProgramDatabase>>")
endif()
# 强制使用 UTF-8 编码
add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")
# 启用 Unicode 支持
add_compile_definitions(UNICODE _UNICODE)


project ("gerrit_client")

#find_package(CURL REQUIRED)

# 将源代码添加到此项目的可执行文件。
add_executable (gerrit_client 
    "gerrit_client.cpp" "gerrit_client.h" 
    "easy_direct_composition/src/obj_tree.h" 
    "easy_direct_composition/src/obj_tree.c" 
    "easy_direct_composition/src/obj_helper.h" 
    "easy_direct_composition/src/obj_helper.cpp" 
    "easy_direct_composition/src/dc_env.h" 
    "easy_direct_composition/src/dc_env.cpp" 
    "easy_direct_composition/src/dc_surface.h" 
    "easy_direct_composition/src/dc_surface.cpp")

target_link_libraries(gerrit_client PRIVATE d3d11 d2d1 dcomp ole32)
#target_link_libraries(gerrit_client PRIVATE CURL::libcurl)

if (CMAKE_VERSION VERSION_GREATER 3.12)
  set_property(TARGET gerrit_client PROPERTY CXX_STANDARD 20)
endif()

# TODO: 如有需要，请添加测试并安装目标。
